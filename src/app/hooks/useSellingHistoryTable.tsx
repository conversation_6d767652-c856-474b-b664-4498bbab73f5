/* eslint-disable no-warning-comments */

import ProductDetailColumn from "@components/SellingDashboard/ProductDetailColumn"
import SellingDashboardTxStatusCol from "@components/SellingDashboard/SellingDashboardTxStatusCol"
import HistoryTableRowActions from "@components/SellingDashboard/SellingHistory/HistoryTableRowActions"
import ConditionColumn from "@components/shared/ConditionColumn"
import { formatDateObj, formatStripePrice } from "@utils/misc"
import { TTableColumn } from "types/table.type"
import { TTransactionDetail } from "types/transactionDetail.type"

const columnWidth = {
  productDetails: 325,
  size: 64,
  conditions: 108,
  soldPrice: 148,
  revenue: 148,
  soldDate: 140,
  status: 150,
  actions: 67,
  invoiceNumber: 200,
}

const useSellingHistoryTable = () => {
  const columns = [
    {
      key: "invoiceNumber",
      title: "Invoice Number",
      width: columnWidth.invoiceNumber,
      render: (record) => record.invoiceNumber,
      sorter: () => {},
    },
    {
      key: "productDetails",
      title: "Product Details",
      width: columnWidth.productDetails,
      render: (record: TTransactionDetail) => {
        return <ProductDetailColumn listingItem={record.listingItem} />
      },
    },
    {
      key: "size",
      title: "Size",
      width: columnWidth.size,
      render: ({ listingItem }: TTransactionDetail) =>
        listingItem?.size?.us ? `US ${listingItem.size.us}` : "-",
    },
    {
      key: "itemCondition",
      dataIndex: "itemCondition",
      title: "Conditions",
      width: columnWidth.conditions,
      render: ({ listingItem }: TTransactionDetail) => (
        <ConditionColumn
          itemCondition={listingItem?.itemCondition}
          packagingCondition={listingItem?.packagingCondition}
        />
      ),
    },
    {
      key: "price",
      title: "Sold Price",
      width: columnWidth.soldPrice,
      headerClassName: "text-right [&>div]:!justify-end",
      contentClassName: "text-right",
      render: (record: TTransactionDetail) =>
        formatStripePrice(record.price, "IDR"),
      sorter: () => {},
    },
    // TODO: Confirm this value with backend
    {
      key: "revenue",
      dataIndex: "revenue",
      title: "Revenue",
      width: columnWidth.revenue,
      headerClassName: "text-right [&>div]:!justify-end",
      contentClassName: "text-right",
      render: (record: TTransactionDetail) =>
        formatStripePrice(record.price, "IDR"),
    },
    {
      key: "createdAt",
      title: "Sold Date",
      width: columnWidth.soldDate,
      render: ({ createdAt = "" }: TTransactionDetail) =>
        createdAt ? formatDateObj(new Date(createdAt)) : "-",
      sorter: () => {},
    },
    {
      key: "status",
      dataIndex: "status",
      title: "Status",
      width: columnWidth.status,
      render: (record: TTransactionDetail) => (
        <SellingDashboardTxStatusCol record={record} />
      ),
    },
    {
      key: "actions",
      title: "Actions",
      width: columnWidth.actions,
      render: (record: TTransactionDetail) => (
        <HistoryTableRowActions record={record} />
      ),
    },
  ] as TTableColumn[]

  return {
    columns,
  }
}

export default useSellingHistoryTable
