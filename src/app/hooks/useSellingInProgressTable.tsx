import { Badge } from "@kickavenue/ui/components/Badge"

import ProductDetailColumn from "@components/SellingDashboard/ProductDetailColumn"
import SellingDashboardTxStatusCol from "@components/SellingDashboard/SellingDashboardTxStatusCol"
import InProgressTableRowActions from "@components/SellingDashboard/SellingInProgress/InProgressTableRowActions"
import ConditionColumn from "@components/shared/ConditionColumn"
import IconTimeBold from "@components/shared/Icons/IconTimeBold"
import { formatDateObj, formatStripePrice } from "@utils/misc"
import { getOrderDeadlineFromStatus } from "@utils/txDetail.utils"
import { TTableColumn } from "types/table.type"
import {
  TOrderStatusTrack,
  TTransactionDetail,
  TTransactionDetailStatus,
} from "types/transactionDetail.type"

const columnWidth = {
  productDetails: 434,
  size: 64,
  conditions: 108,
  soldPrice: 148,
  soldDate: 140,
  status: 200,
  actions: 67,
  invoiceNumber: 200,
}

const useSellingInProgressTable = () => {
  const columns = [
    {
      key: "invoiceNumber",
      title: "Invoice Number",
      width: columnWidth.invoiceNumber,
      render: (record) => record.invoiceNumber,
      sorter: () => {},
    },
    {
      key: "productDetails",
      title: "Product Details",
      width: columnWidth.productDetails,
      render: (record: TTransactionDetail) => {
        return <ProductDetailColumn listingItem={record.listingItem} />
      },
    },
    {
      key: "size",
      title: "Size",
      width: columnWidth.size,
      render: ({ listingItem }: TTransactionDetail) =>
        listingItem?.size?.us ? `US ${listingItem.size.us}` : "-",
    },
    {
      key: "itemCondition",
      dataIndex: "itemCondition",
      title: "Conditions",
      width: columnWidth.conditions,
      render: ({ listingItem }: TTransactionDetail) => (
        <ConditionColumn
          itemCondition={listingItem?.itemCondition}
          packagingCondition={listingItem?.packagingCondition}
        />
      ),
    },
    {
      key: "price",
      title: "Sold Price",
      width: columnWidth.soldPrice,
      headerClassName: "text-right [&>div]:!justify-end",
      contentClassName: "text-right",
      render: (record: TTransactionDetail) =>
        formatStripePrice(record.price, "IDR", ","),
      sorter: () => {},
    },
    {
      key: "createdAt",
      title: "Sold Date",
      width: columnWidth.soldDate,
      render: ({ createdAt = "" }: TTransactionDetail) =>
        createdAt ? formatDateObj(new Date(createdAt)) : "-",
      sorter: () => {},
    },
    {
      key: "status",
      dataIndex: "status",
      title: "Status",
      width: columnWidth.status,
      render: (record: TTransactionDetail) => {
        const statusWaitDelivery =
          TTransactionDetailStatus.WaitingSellerDelivering
        const statusWaitSellerConfirm =
          TTransactionDetailStatus.WaitingSellerConfirmation
        const statusToCount =
          record.status === statusWaitDelivery
            ? TOrderStatusTrack.OrderConfirmedBySeller
            : TOrderStatusTrack.OrderCreated

        const { time } = getOrderDeadlineFromStatus(record, statusToCount)
        return (
          <div className="flex flex-col gap-2">
            <SellingDashboardTxStatusCol record={record} />
            {[statusWaitDelivery, statusWaitSellerConfirm].includes(
              record.status,
            ) && (
              <div className="flex">
                <Badge
                  type="negative"
                  text={time?.slice(0, -5) || "-"}
                  size="md"
                  iconLeft={IconTimeBold}
                  className="[&>svg]:!text-[#FF2323]"
                />
              </div>
            )}
          </div>
        )
      },
    },
    {
      key: "actions",
      title: "Actions",
      width: columnWidth.actions,
      render: (record: TTransactionDetail) => (
        <InProgressTableRowActions record={record} />
      ),
    },
  ] as TTableColumn[]

  return { columns }
}

export default useSellingInProgressTable
