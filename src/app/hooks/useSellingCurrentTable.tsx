import CheckboxHeader from "@components/SellingDashboard/CheckboxHeader"
import ProductDetailColumn from "@components/SellingDashboard/ProductDetailColumn"
import CurrentTableRowActions from "@components/SellingDashboard/SellingCurrent/CurrentTable/CurrentTableRowActions"
import CurrentTableStatus from "@components/SellingDashboard/SellingCurrent/CurrentTable/CurrentTableStatus"
import ConditionColumn from "@components/shared/ConditionColumn"
import {
  formatDateObj,
  formatPriceMinUnitVal,
  getStripAmount,
} from "@utils/misc"
import { getSellingCurrentLowestAsk } from "@utils/selling"
import { formatCurrency } from "@utils/separator"
import { useSellingCurrentStore } from "stores/sellingCurrentStore"
import { TListingItem } from "types/listingItem.type"
import { TTableColumn } from "types/table.type"

const columnWidth = {
  checkbox: 44,
  productDetails: 248,
  size: 64,
  conditions: 108,
  listingPrice: 148,
  standardLowestAsk: 148,
  expressLowestAsk: 148,
  highestOffer: 148,
  expiryDate: 140,
  status: 128,
  actions: 67,
}

// eslint-disable-next-line max-lines-per-function
const useSellingCurrentTable = () => {
  const { selectedRowKeys, setSelectedRowKeys, sellingCurrentData } =
    useSellingCurrentStore()
  const columns = [
    {
      key: "checkbox",
      title: "",
      width: columnWidth.checkbox,
      renderHeader: () => (
        <CheckboxHeader
          selectedRowKeys={selectedRowKeys}
          setSelectedRowKeys={setSelectedRowKeys}
          data={sellingCurrentData}
        />
      ),
    },
    {
      key: "productDetails",
      title: "Product Details",
      width: columnWidth.productDetails,
      render: (record: TListingItem) => {
        return <ProductDetailColumn listingItem={record} />
      },
    },
    {
      key: "size",
      dataIndex: "size",
      title: "Size",
      width: columnWidth.size,
      render: (record: TListingItem) =>
        record?.size?.us ? `US ${record.size.us}` : "-",
    },
    {
      key: "itemCondition",
      title: "Conditions",
      width: columnWidth.conditions,
      render: (record: TListingItem) => (
        <ConditionColumn
          itemCondition={record?.itemCondition}
          packagingCondition={record?.packagingCondition}
        />
      ),
    },
    {
      key: "sellingPrice",
      title: "Listing Price",
      width: columnWidth.listingPrice,
      headerClassName: "text-right [&>div]:!justify-end",
      contentClassName: "text-right",
      render: (item: TListingItem) =>
        formatCurrency(
          formatPriceMinUnitVal(item?.sellingPrice?.minUnitVal || 0) || 0,
          ",",
          "IDR",
        ),
      sorter: () => {},
    },
    {
      key: "standardLowestAsk",
      dataIndex: "standardLowestAsk",
      title: "Standard Lowest Ask",
      width: columnWidth.standardLowestAsk,
      headerClassName: "text-right [&>div]:!justify-end",
      contentClassName: "text-right",
      render: (listing: TListingItem) => getSellingCurrentLowestAsk(listing),
    },
    {
      key: "expressLowestAsk",
      dataIndex: "expressLowestAsk",
      title: "Express Lowest Ask",
      width: columnWidth.expressLowestAsk,
      headerClassName: "text-right [&>div]:!justify-end",
      contentClassName: "text-right",
      render: (listing: TListingItem) => getSellingCurrentLowestAsk(listing),
    },
    {
      key: "highestOfferAmount",
      title: "Highest Offer",
      width: columnWidth.highestOffer,
      headerClassName: "text-right [&>div]:!justify-end",
      contentClassName: "text-right",
      render: (item: TListingItem) =>
        formatCurrency(
          getStripAmount((item?.highestOfferAmount as number) || 0),
          ",",
          "IDR",
        ),
    },
    {
      key: "expiryDate",
      dataIndex: "expiryDate",
      title: "Expiry Date",
      width: columnWidth.expiryDate,
      render: ({ expiryDate = "" }: TListingItem) =>
        expiryDate ? formatDateObj(new Date(expiryDate)) : "-",
      sorter: () => {},
    },
    {
      key: "status",
      title: "Status",
      width: columnWidth.status,
      render: (record: TListingItem) => <CurrentTableStatus record={record} />,
    },
    {
      key: "actions",
      title: "Actions",
      width: columnWidth.actions,
      render: (record: TListingItem) => (
        <CurrentTableRowActions record={record} />
      ),
    },
  ] as TTableColumn[]
  return {
    columns,
  }
}

export default useSellingCurrentTable
