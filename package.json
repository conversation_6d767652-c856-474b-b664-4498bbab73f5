{"name": "marketplace-web", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "lint": "next lint --fix", "start": "next start -p 3000", "cy:open": "cypress open", "cy:run": "cypress run --component --record --key", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "dev-next": "next dev -p 4000", "dev": "concurrently \"pnpm run dev-next\"", "dev-https": "next-dev-https --https --qr --port 4430", "fix": "pnpm run lint && pnpm exec tsc -p tsconfig.json", "prepare": "husky", "local-prod": "pnpm lint && pnpm build && pnpm start"}, "dependencies": {"@aws-sdk/client-cognito-identity-provider": "^3.620.0", "@hookform/resolvers": "^4.1.3", "@kickavenue/ui": "^0.0.0-beta.88", "@tailwindcss/typography": "^0.5.13", "@tanstack/react-query": "^5.51.11", "@tanstack/react-query-devtools": "^5.51.1", "@types/qs": "^6.9.16", "axios": "^1.7.2", "chart.js": "^4.4.4", "class-variance-authority": "^0.7.0", "date-fns": "^3.6.0", "immer": "^10.1.1", "lodash": "^4.17.21", "moment": "^2.30.1", "next": "14.2.3", "next-auth": "^4.24.7", "next-dev-https": "^0.13.3", "react": "^18", "react-chartjs-2": "^5.2.0", "react-day-picker": "^9.0.8", "react-dom": "^18", "react-feather": "^2.0.10", "react-hook-form": "^7.52.2", "react-intersection-observer": "^9.13.1", "react-loading-skeleton": "^3.5.0", "react-select": "^5.8.3", "react-select-async-paginate": "^0.7.6", "sass": "^1.77.6", "sharp": "^0.34.2", "use-mask-input": "^3.4.0", "zod": "^3.23.8", "zustand": "^4.5.4"}, "devDependencies": {"@next/eslint-plugin-next": "14.2.5", "@shopify/eslint-plugin": "45.0.0", "@types/lodash": "^4.17.7", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^7.15.0", "@typescript-eslint/parser": "^7.16.0", "autoprefixer": "^10.4.19", "concurrently": "^8.2.2", "eslint": "^8", "eslint-config-next": "14.2.3", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react-hooks": "4.6.2", "eslint-plugin-tailwindcss": "^3.17.4", "eslint-plugin-unused-imports": "^4.0.0", "husky": "^9.0.11", "next-transpile-modules": "^10.0.1", "postcss": "^8", "prettier": "^3.3.0", "prettier-plugin-tailwindcss": "^0.6.1", "qs": "^6.13.0", "tailwindcss": "^3.4.1", "typescript": "~5.4.0"}}